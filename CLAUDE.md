# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## High-level Code Architecture and Structure

This is a **Model Context Protocol (MCP) Server for NocoBase** - a comprehensive TypeScript-based server that provides intelligent tools to interact with NocoBase applications through their REST API.

### Core Architecture

The server follows a modular architecture with clear separation of concerns:

- **Entry Point**: `src/index.ts` - Main server setup, configuration parsing, and tool registration
- **API Client**: `src/client.ts` - Comprehensive NocoBase API client handling all HTTP communications
- **Tool Modules**: `src/tools/` - Modular tool implementations organized by functionality
  - `collections.ts` - Collection management (CRUD operations)
  - `records.ts` - Record operations with batch support
  - `schema.ts` - Schema analysis and UI component inspection
  - `routes.ts` - Route and menu management
  - `blocks.ts` - Block template generation and management
- **Resources**: `src/resources/` - MCP resource definitions
- **Utilities**: `src/utils.ts`, `src/verification-strategy.ts` - Helper functions and validation logic

### Key Design Patterns

1. **Modular Tool Registration**: Each tool category has its own registration function
2. **Type-Safe API Client**: Strong TypeScript interfaces for all NocoBase API responses
3. **Error Handling**: Comprehensive error handling with meaningful user messages
4. **Configuration Management**: Flexible configuration via command-line args or environment variables

### NocoBase API Integration

The server integrates deeply with NocoBase's REST API, including:
- **Collections API**: Full CRUD operations on database collections
- **Records API**: Data management with filtering, sorting, and pagination
- **UI Schema API**: Page structure analysis and block manipulation
- **Routes API**: Menu and page management
- **Fields API**: Schema field management

## Commonly Used Commands

### Development
```bash
# Install dependencies
npm install

# Development mode (watch mode)
npm run dev

# Build the project
npm run build

# Start the server
npm start

# Run tests
npm test

# Lint code
npm run lint
npm run lint:fix
```

### Testing and Debugging
The project includes extensive test scripts in the `scripts/` directory for validating functionality:

```bash
# Test basic MCP functions
node scripts/test-mcp-basic-functions.js

# Verify student data integrity
node scripts/verify-students-data.js

# Analyze menu structure
node scripts/analyze-menu-structure.js

# Test API connectivity
node scripts/test-api-connection.js

# Debug schema operations
node scripts/debug-schema.js
```

### Server Configuration
The server requires three essential parameters:

```bash
# Using command line arguments
node dist/index.js \
  --base-url https://your-nocobase-instance/api \
  --token your-auth-token \
  --app your-app-id

# Using environment variables
export NOCOBASE_BASE_URL=https://your-nocobase-instance/api
export NOCOBASE_TOKEN=your-auth-token
export NOCOBASE_APP=your-app-id
node dist/index.js
```

## Known Limitations and Considerations

### Schema API Limitations
- **Block Creation**: Direct block creation via Schema API is limited due to NocoBase internal validation issues
- **Alternative Approach**: Use provided templates and frontend interface for block management
- **Error Pattern**: Common error "Cannot set properties of undefined (setting 'name')" indicates Schema API issues

### API Rate Limiting
- Built-in timeout of 30 seconds for all API calls
- Comprehensive error handling for network and API failures
- Retry mechanisms should be implemented at the application level

### Testing Environment
The project includes a pre-configured testing environment:
- **Test Instance**: https://n.astra.xin/api
- **Test App**: mcp_playground
- **Test Credentials**: neo / neo@123
- **Test Data**: Pre-populated student records for validation

## Development Workflow

1. **Tool Development**: Add new tools in appropriate `src/tools/` modules
2. **Type Safety**: Ensure all API responses have proper TypeScript interfaces
3. **Error Handling**: Implement comprehensive error handling for all operations
4. **Testing**: Use existing test scripts as templates for new functionality
5. **Documentation**: Update tool descriptions and usage examples

## Project Status

- **Core Functionality**: 85% complete
- **API Coverage**: Comprehensive coverage of NocoBase REST API
- **Documentation**: Extensive documentation with troubleshooting guides
- **Testing**: Well-tested with real-world scenarios