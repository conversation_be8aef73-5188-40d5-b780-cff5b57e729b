/**
 * Schema 查找工具 - 借鉴 dify workflow 的成功经验
 * 用于在复杂的 NocoBase Schema 结构中精确定位目标元素
 */

/**
 * 递归查找指定键值对，并返回目标字段值
 * @param data 要搜索的数据（字典）
 * @param targetKey 要匹配的键
 * @param targetValue 要匹配的值
 * @param returnKey 要返回的字段名，默认为 "x-uid"
 * @returns 匹配项的指定字段值或 null
 */
export function findValueByKey(
  data: any,
  targetKey: string,
  targetValue: string,
  returnKey: string = "x-uid"
): string | null {
  if (!data || typeof data !== 'object') {
    return null;
  }

  // 检查当前层是否匹配
  if (data[targetKey] === targetValue) {
    return data[returnKey] || null;
  }

  // 递归搜索 properties 中的每个属性
  if (data.properties && typeof data.properties === 'object') {
    for (const prop of Object.values(data.properties)) {
      const result = findValueByKey(prop, targetKey, targetValue, returnKey);
      if (result) {
        return result;
      }
    }
  }

  // 递归搜索其他可能的子节点
  for (const value of Object.values(data)) {
    if (value && typeof value === 'object') {
      if (Array.isArray(value)) {
        for (const item of value) {
          const result = findValueByKey(item, targetKey, targetValue, returnKey);
          if (result) {
            return result;
          }
        }
      } else {
        const result = findValueByKey(value, targetKey, targetValue, returnKey);
        if (result) {
          return result;
        }
      }
    }
  }

  return null;
}

/**
 * 查找页面中的 Grid 容器 UID
 * @param pageSchema 页面 Schema 数据
 * @returns Grid 容器的 UID
 */
export function findPageGridUid(pageSchema: any): string | null {
  // 首先尝试查找标准的 page:addBlock 初始化器
  let gridUid = findValueByKey(pageSchema, "x-initializer", "page:addBlock");

  if (gridUid) {
    return gridUid;
  }

  // 如果找不到，尝试查找其他可能的 Grid 初始化器
  const gridInitializers = [
    "page:addBlock",
    "Grid-page:addBlock",
    "schema-initializer-Grid-page:addBlock"
  ];

  for (const initializer of gridInitializers) {
    gridUid = findValueByKey(pageSchema, "x-initializer", initializer);
    if (gridUid) {
      return gridUid;
    }
  }

  // 如果还是找不到，尝试查找 Grid 组件
  gridUid = findValueByKey(pageSchema, "x-component", "Grid");
  if (gridUid) {
    return gridUid;
  }

  return null;
}

/**
 * 查找表格的列配置区域 UID
 * @param tableSchema 表格 Schema 数据
 * @returns 列配置区域的 UID
 */
export function findTableColumnsUid(tableSchema: any): string | null {
  return findValueByKey(tableSchema, "x-initializer", "table:configureColumns");
}

/**
 * 查找表格的操作列 UID
 * @param tableSchema 表格 Schema 数据
 * @returns 操作列的 UID
 */
export function findTableActionsUid(tableSchema: any): string | null {
  return findValueByKey(tableSchema, "x-initializer", "table:configureItemActions");
}

/**
 * 查找表格的操作栏 UID
 * @param tableSchema 表格 Schema 数据
 * @returns 操作栏的 UID
 */
export function findTableActionBarUid(tableSchema: any): string | null {
  return findValueByKey(tableSchema, "x-initializer", "table:configureActions");
}

/**
 * 生成随机 UID（使用 dify workflow 的方式）
 * @param length UID 长度，默认 11
 * @returns 随机 UID 字符串
 */
export function generateRandomUid(length: number = 11): string {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
  return Array.from({ length }, () => chars[Math.floor(Math.random() * chars.length)]).join('');
}
