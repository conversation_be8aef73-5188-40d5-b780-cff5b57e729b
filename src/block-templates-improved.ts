/**
 * 改进的区块模板 - 基于 dify workflow 的成功经验
 * 使用完整且符合 NocoBase 规范的 Schema 结构
 */

import { generateRandomUid } from './utils/schema-finder.js';

/**
 * 创建改进的表格区块 Schema
 * 完全参考 dify workflow 中成功的表格区块结构
 */
export function createTableBlockSchemaImproved(options: {
  collectionName: string;
  dataSource?: string;
  title?: string;
}): any {
  const { collectionName, dataSource = 'main', title } = options;
  
  // 生成所有需要的随机 UID
  const ids = {
    schemaRoot: generateRandomUid(),
    gridCol: generateRandomUid(),
    cardItem: generateRandomUid(),
    actions: generateRandomUid(),
    table: generateRandomUid(),
    actionColumn: generateRandomUid(),
    space: generateRandomUid()
  };

  const listAction = `${collectionName}:list`;

  // 完整的 Schema 结构（完全参考 dify workflow）
  return {
    "_isJSONSchemaObject": true,
    "version": "2.0",
    "type": "void",
    "x-component": "Grid.Row",
    "properties": {
      [ids.gridCol]: {
        "_isJSONSchemaObject": true,
        "version": "2.0",
        "type": "void",
        "x-component": "Grid.Col",
        "properties": {
          [ids.cardItem]: {
            "_isJSONSchemaObject": true,
            "version": "2.0",
            "type": "void",
            "x-decorator": "TableBlockProvider",
            "x-acl-action": listAction,
            "x-use-decorator-props": "useTableBlockDecoratorProps",
            "x-decorator-props": {
              "collection": collectionName,
              "dataSource": dataSource,
              "action": "list",
              "params": {
                "pageSize": 20
              },
              "rowKey": "id",
              "showIndex": true,
              "dragSort": false
            },
            "x-toolbar": "BlockSchemaToolbar",
            "x-settings": "blockSettings:table",
            "x-component": "CardItem",
            "x-filter-targets": [],
            "properties": {
              "actions": {
                "_isJSONSchemaObject": true,
                "version": "2.0",
                "type": "void",
                "x-initializer": "table:configureActions",
                "x-component": "ActionBar",
                "x-component-props": {
                  "style": {
                    "marginBottom": "var(--nb-spacing)"
                  }
                },
                "x-uid": ids.actions,
                "name": "actions",
                "x-app-version": "1.4.12"
              },
              [ids.table]: {
                "_isJSONSchemaObject": true,
                "version": "2.0",
                "type": "array",
                "x-initializer": "table:configureColumns",
                "x-component": "TableV2",
                "x-use-component-props": "useTableBlockProps",
                "x-component-props": {
                  "rowKey": "id",
                  "rowSelection": {
                    "type": "checkbox"
                  }
                },
                "properties": {
                  "actions": {
                    "_isJSONSchemaObject": true,
                    "version": "2.0",
                    "type": "void",
                    "title": "{{ t(\"Actions\") }}",
                    "x-action-column": "actions",
                    "x-decorator": "TableV2.Column.ActionBar",
                    "x-component": "TableV2.Column",
                    "x-toolbar": "TableColumnSchemaToolbar",
                    "x-initializer": "table:configureItemActions",
                    "x-settings": "fieldSettings:TableColumn",
                    "x-toolbar-props": {
                      "initializer": "table:configureItemActions"
                    },
                    "properties": {
                      [ids.space]: {
                        "_isJSONSchemaObject": true,
                        "version": "2.0",
                        "type": "void",
                        "x-decorator": "DndContext",
                        "x-component": "Space",
                        "x-component-props": {
                          "split": "|"
                        },
                        "x-uid": ids.space,
                        "name": ids.space,
                        "x-app-version": "1.4.12"
                      }
                    },
                    "x-uid": ids.actionColumn,
                    "name": "actions",
                    "x-app-version": "1.4.12"
                  }
                },
                "x-uid": ids.table,
                "name": ids.table,
                "x-app-version": "1.4.12"
              }
            },
            "x-uid": ids.cardItem,
            "name": ids.cardItem,
            "x-app-version": "1.4.12"
          }
        },
        "x-uid": ids.gridCol,
        "name": ids.gridCol,
        "x-app-version": "1.4.12"
      }
    },
    "name": generateRandomUid(10),
    "x-uid": ids.schemaRoot,
    "x-app-version": "1.4.12"
  };
}

/**
 * 创建表格列的 Schema
 * 用于动态添加字段列
 */
export function createTableColumnSchema(options: {
  fieldName: string;
  collectionName: string;
}): any {
  const { fieldName, collectionName } = options;
  
  const fieldUid = generateRandomUid();
  const collectionField = `${collectionName}.${fieldName}`;

  return {
    "_isJSONSchemaObject": true,
    "version": "2.0",
    "type": "void",
    "x-decorator": "TableV2.Column.Decorator",
    "x-toolbar": "TableColumnSchemaToolbar",
    "x-settings": "fieldSettings:TableColumn",
    "x-component": "TableV2.Column",
    "properties": {
      [fieldName]: {
        "_isJSONSchemaObject": true,
        "version": "2.0",
        "name": fieldName,
        "x-collection-field": collectionField,
        "x-component": "CollectionField",
        "x-component-props": {
          "ellipsis": true
        },
        "x-read-pretty": true,
        "x-decorator": null,
        "x-decorator-props": {
          "labelStyle": {
            "display": "none"
          }
        },
        "x-uid": fieldUid,
        "x-app-version": "1.4.12"
      }
    },
    "name": generateRandomUid(10),
    "x-uid": generateRandomUid(),
    "x-app-version": "1.4.12"
  };
}

/**
 * 创建简化的 Markdown 区块 Schema（用于测试）
 */
export function createMarkdownBlockSchemaImproved(options: {
  title?: string;
  content?: string;
}): any {
  const { title = 'Markdown', content = '# Hello World\n\nThis is a markdown block.' } = options;
  
  const ids = {
    schemaRoot: generateRandomUid(),
    gridCol: generateRandomUid(),
    cardItem: generateRandomUid(),
    markdown: generateRandomUid()
  };

  return {
    "_isJSONSchemaObject": true,
    "version": "2.0",
    "type": "void",
    "x-component": "Grid.Row",
    "properties": {
      [ids.gridCol]: {
        "_isJSONSchemaObject": true,
        "version": "2.0",
        "type": "void",
        "x-component": "Grid.Col",
        "properties": {
          [ids.cardItem]: {
            "_isJSONSchemaObject": true,
            "version": "2.0",
            "type": "void",
            "x-decorator": "MarkdownBlockProvider",
            "x-decorator-props": {},
            "x-toolbar": "BlockSchemaToolbar",
            "x-settings": "blockSettings:markdown",
            "x-component": "CardItem",
            "properties": {
              [ids.markdown]: {
                "_isJSONSchemaObject": true,
                "version": "2.0",
                "type": "void",
                "x-component": "Markdown.Void",
                "x-editable": false,
                "x-component-props": {
                  "content": content
                },
                "x-uid": ids.markdown,
                "name": ids.markdown,
                "x-app-version": "1.4.12"
              }
            },
            "x-uid": ids.cardItem,
            "name": ids.cardItem,
            "x-app-version": "1.4.12"
          }
        },
        "x-uid": ids.gridCol,
        "name": ids.gridCol,
        "x-app-version": "1.4.12"
      }
    },
    "name": generateRandomUid(10),
    "x-uid": ids.schemaRoot,
    "x-app-version": "1.4.12"
  };
}
