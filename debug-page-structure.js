#!/usr/bin/env node

/**
 * 调试页面结构 - 找到正确的插入点
 */

import { NocoBaseClient } from './dist/client.js';

const config = {
  baseUrl: 'https://n.astra.xin/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQyNzYwNzgsImV4cCI6MzMzMTE4NzYwNzh9.pcor4Xbillx57TzC44_yhQmCR6AeFCKWYmrXkbO67uM',
  app: 'mcp_playground'
};

function findAllInitializers(obj, path = '') {
  const results = [];
  
  if (typeof obj !== 'object' || obj === null) {
    return results;
  }
  
  // 检查当前对象是否有 x-initializer
  if (obj['x-initializer']) {
    results.push({
      path: path,
      initializer: obj['x-initializer'],
      uid: obj['x-uid'] || 'no-uid',
      component: obj['x-component'] || 'no-component'
    });
  }
  
  // 递归搜索
  for (const [key, value] of Object.entries(obj)) {
    if (typeof value === 'object' && value !== null) {
      const newPath = path ? `${path}.${key}` : key;
      results.push(...findAllInitializers(value, newPath));
    }
  }
  
  return results;
}

async function debugPageStructure() {
  console.log('🔍 调试页面结构，寻找正确的插入点...\n');
  
  const client = new NocoBaseClient(config);
  
  try {
    const targetPageUid = 'cafh7yoyd6w';
    console.log(`📋 分析页面: MCP 测试页面 (UID: ${targetPageUid})\n`);
    
    // 获取页面 Schema
    const pageSchema = await client.getPageSchema(targetPageUid);

    console.log('📄 页面 Schema 响应:');
    console.log(JSON.stringify(pageSchema, null, 2).substring(0, 1000) + '...\n');

    const schemaData = pageSchema.data || pageSchema;

    // 查找所有的初始化器
    const initializers = findAllInitializers(schemaData);
    
    console.log('🎯 找到的所有初始化器：\n');
    initializers.forEach((init, index) => {
      console.log(`${index + 1}. ${init.initializer}`);
      console.log(`   UID: ${init.uid}`);
      console.log(`   组件: ${init.component}`);
      console.log(`   路径: ${init.path}`);
      console.log('');
    });
    
    // 特别查找页面级别的初始化器
    const pageInitializers = initializers.filter(init => 
      init.initializer.includes('page') || 
      init.initializer.includes('addBlock') ||
      init.initializer.includes('Grid')
    );
    
    console.log('🎯 页面级别的初始化器：\n');
    pageInitializers.forEach((init, index) => {
      console.log(`${index + 1}. ${init.initializer}`);
      console.log(`   UID: ${init.uid}`);
      console.log(`   组件: ${init.component}`);
      console.log('');
    });
    
    // 查找成功案例中的 UID
    const targetUid = '0z6ai5gvpgp';
    const targetInit = initializers.find(init => init.uid === targetUid);
    
    if (targetInit) {
      console.log(`🎉 找到成功案例的 UID: ${targetUid}`);
      console.log(`   初始化器: ${targetInit.initializer}`);
      console.log(`   组件: ${targetInit.component}`);
      console.log(`   路径: ${targetInit.path}`);
    } else {
      console.log(`❌ 未找到成功案例的 UID: ${targetUid}`);
    }
    
    // 尝试使用找到的 UID 进行测试
    if (pageInitializers.length > 0) {
      const testUid = pageInitializers[0].uid;
      console.log(`\n🧪 尝试使用 UID: ${testUid} 进行测试插入...`);
      
      const { createMarkdownBlockSchemaImproved } = await import('./dist/block-templates-improved.js');
      const markdownSchema = createMarkdownBlockSchemaImproved({
        title: 'Debug Test',
        content: '# 🔧 调试测试\n\n这是一个调试测试区块，用于验证正确的插入点。\n\n时间：' + new Date().toLocaleString()
      });
      
      try {
        const response = await client.client.post(
          `/uiSchemas:insertAdjacent/${testUid}`,
          {
            schema: markdownSchema,
            wrap: null
          },
          {
            params: { position: 'beforeEnd' }
          }
        );
        
        console.log('✅ 测试插入成功！');
        console.log(`   响应状态: ${response.status}`);
        console.log(`   区块 UID: ${markdownSchema['x-uid']}`);
        
      } catch (error) {
        console.log('❌ 测试插入失败:');
        console.log(`   错误: ${error.message}`);
        if (error.response?.data) {
          console.log(`   详情: ${JSON.stringify(error.response.data, null, 2)}`);
        }
      }
    }
    
  } catch (error) {
    console.error('❌ 调试失败:', error.message);
    if (error.response?.data) {
      console.error('API 错误详情:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行调试
debugPageStructure().catch(console.error);
